# تحسينات APK المتقدمة
# ملف إعدادات إضافية لتحسين حجم وأداء APK

android {
    // تحسينات الضغط والتعبئة
    aaptOptions {
        cruncherEnabled = true
        useNewCruncher = true
        
        // تحسين الموارد
        additionalParameters "--no-version-vectors"
    }
    
    // تحسينات Dex
    dexOptions {
        javaMaxHeapSize "4g"
        preDexLibraries = true
        maxProcessCount = 4
        threadCount = 4
    }
    
    // تحسينات التجميع
    compileOptions {
        incremental = true
    }
    
    // تحسينات Lint
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
        disable 'InvalidPackage'
        disable 'UnusedResources'
        disable 'VectorDrawableCompat'
        disable 'GoogleAppIndexingWarning'
    }
    
    // تحسينات الموارد
    resourcePrefix "app_"
    
    // تحسينات البناء
    buildFeatures {
        viewBinding = false
        dataBinding = false
    }
}

// تحسينات إضافية للإنتاج
if (project.hasProperty("release")) {
    android {
        buildTypes {
            release {
                // تحسينات متقدمة للإنتاج
                zipAlignEnabled true
                crunchPngs true
                
                // تحسين الموارد
                resourceShrinking true
                
                // تحسين الكود
                codeShrinker "R8"
                
                // إعدادات ProGuard متقدمة
                proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 
                             'proguard-rules.pro',
                             'proguard-advanced.pro'
            }
        }
    }
}

// تحسينات الذاكرة
gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xmx2048m"
    }
}

// تحسينات التوقيع
android {
    signingConfigs {
        release {
            // إعدادات التوقيع للإنتاج
            v1SigningEnabled true
            v2SigningEnabled true
            v3SigningEnabled true
            v4SigningEnabled true
        }
    }
}
