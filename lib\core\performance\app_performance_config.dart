import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

/// إعدادات تحسين الأداء للتطبيق
class AppPerformanceConfig {
  static const bool _isProduction = kReleaseMode;
  
  /// تهيئة تحسينات الأداء
  static Future<void> initialize() async {
    if (_isProduction) {
      await _initializeProductionOptimizations();
    } else {
      await _initializeDevelopmentOptimizations();
    }
  }
  
  /// تحسينات الإنتاج
  static Future<void> _initializeProductionOptimizations() async {
    // تعطيل التحقق من الأخطاء في الإنتاج
    FlutterError.onError = (FlutterErrorDetails details) {
      // تسجيل الأخطاء فقط دون عرضها
      debugPrint('خطأ في التطبيق: ${details.exception}');
    };
    
    // تحسين الذاكرة
    await _optimizeMemoryUsage();
    
    // تحسين الرسوميات
    await _optimizeGraphics();
    
    // تحسين الشبكة
    await _optimizeNetworking();
  }
  
  /// تحسينات التطوير
  static Future<void> _initializeDevelopmentOptimizations() async {
    // إعدادات التطوير
    debugPrint('تم تفعيل وضع التطوير');
  }
  
  /// تحسين استخدام الذاكرة
  static Future<void> _optimizeMemoryUsage() async {
    // تنظيف الذاكرة بشكل دوري
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // تنظيف الصور غير المستخدمة
      imageCache.clear();
      imageCache.clearLiveImages();
      
      // تحديد حجم cache الصور
      imageCache.maximumSize = 100;
      imageCache.maximumSizeBytes = 50 * 1024 * 1024; // 50 MB
    });
  }
  
  /// تحسين الرسوميات
  static Future<void> _optimizeGraphics() async {
    // تحسين الرسوميات للأجهزة المختلفة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // تحسين معدل الإطارات
      WidgetsBinding.instance.scheduleWarmUpFrame();
    });
  }
  
  /// تحسين الشبكة
  static Future<void> _optimizeNetworking() async {
    // تحسين طلبات الشبكة
    // يمكن إضافة تحسينات HTTP هنا
  }
  
  /// إعدادات تحسين الواجهة
  static Widget optimizedApp({required Widget child}) {
    return RepaintBoundary(
      child: child,
    );
  }
  
  /// إعدادات تحسين القوائم
  static const ScrollPhysics optimizedScrollPhysics = BouncingScrollPhysics(
    parent: AlwaysScrollableScrollPhysics(),
  );
  
  /// إعدادات تحسين الصور
  static const ImageCacheConfig optimizedImageCache = ImageCacheConfig(
    maximumSize: 100,
    maximumSizeBytes: 50 * 1024 * 1024, // 50 MB
  );
  
  /// إعدادات تحسين الانيميشن
  static const Duration optimizedAnimationDuration = Duration(milliseconds: 200);
  static const Curve optimizedAnimationCurve = Curves.easeInOut;
  
  /// تحسين الخطوط
  static TextStyle optimizedTextStyle(TextStyle style) {
    return style.copyWith(
      fontFamily: 'Cairo',
      fontFamilyFallback: const ['Arial', 'sans-serif'],
    );
  }
  
  /// تحسين الألوان
  static Color optimizedColor(Color color) {
    // تحسين الألوان للأداء
    return color;
  }
  
  /// تحسين الظلال
  static List<BoxShadow> optimizedShadow({
    Color color = Colors.black26,
    double blurRadius = 4.0,
    Offset offset = const Offset(0, 2),
  }) {
    return [
      BoxShadow(
        color: color,
        blurRadius: blurRadius,
        offset: offset,
      ),
    ];
  }
  
  /// تحسين الحدود
  static BorderRadius optimizedBorderRadius([double radius = 8.0]) {
    return BorderRadius.circular(radius);
  }
  
  /// تحسين الحشو
  static EdgeInsets optimizedPadding({
    double all = 0.0,
    double horizontal = 0.0,
    double vertical = 0.0,
    double top = 0.0,
    double bottom = 0.0,
    double left = 0.0,
    double right = 0.0,
  }) {
    if (all > 0) return EdgeInsets.all(all);
    if (horizontal > 0 || vertical > 0) {
      return EdgeInsets.symmetric(
        horizontal: horizontal,
        vertical: vertical,
      );
    }
    return EdgeInsets.only(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
    );
  }
  
  /// تحسين الأزرار
  static ButtonStyle optimizedButtonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    double? elevation,
    EdgeInsets? padding,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation ?? 2.0,
      padding: padding ?? const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 8.0,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: optimizedBorderRadius(),
      ),
    );
  }
  
  /// تحسين الحاويات
  static Widget optimizedContainer({
    Widget? child,
    Color? color,
    double? width,
    double? height,
    EdgeInsets? padding,
    EdgeInsets? margin,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
  }) {
    return RepaintBoundary(
      child: Container(
        width: width,
        height: height,
        padding: padding,
        margin: margin,
        decoration: BoxDecoration(
          color: color,
          borderRadius: borderRadius ?? optimizedBorderRadius(),
          boxShadow: boxShadow,
        ),
        child: child,
      ),
    );
  }
  
  /// تحسين القوائم
  static Widget optimizedListView({
    required List<Widget> children,
    ScrollPhysics? physics,
    EdgeInsets? padding,
    bool shrinkWrap = false,
  }) {
    return RepaintBoundary(
      child: ListView(
        physics: physics ?? optimizedScrollPhysics,
        padding: padding,
        shrinkWrap: shrinkWrap,
        children: children,
      ),
    );
  }
  
  /// تحسين الشبكات
  static Widget optimizedGridView({
    required List<Widget> children,
    required int crossAxisCount,
    double crossAxisSpacing = 8.0,
    double mainAxisSpacing = 8.0,
    EdgeInsets? padding,
    bool shrinkWrap = false,
  }) {
    return RepaintBoundary(
      child: GridView.count(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
        padding: padding,
        shrinkWrap: shrinkWrap,
        physics: optimizedScrollPhysics,
        children: children,
      ),
    );
  }
  
  /// معلومات الأداء
  static Map<String, dynamic> getPerformanceInfo() {
    return {
      'isProduction': _isProduction,
      'imageCacheSize': imageCache.currentSize,
      'imageCacheBytes': imageCache.currentSizeBytes,
      'platform': defaultTargetPlatform.name,
    };
  }
}
