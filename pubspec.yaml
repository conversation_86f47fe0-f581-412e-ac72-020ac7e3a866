name: sharia_law_app
description: "ساحة الشريعة والقانون - تطبيق شامل للطلاب والمختصين في الشريعة والقانون"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # PDF Viewer (استخدام Syncfusion فقط - أفضل وأخف)
  syncfusion_flutter_pdfviewer: ^30.1.39
  syncfusion_flutter_pdf: ^30.1.39

  # UI Components
  google_fonts: ^6.2.1

  # State Management
  provider: ^6.1.2

  # Network & Connectivity
  connectivity_plus: ^6.0.5

  # Animations
  animations: ^2.0.11

  # File & Image Handling (محسن)
  image_picker: ^1.0.4
  file_picker: ^8.0.0+1
  path_provider: ^2.1.1
  path: ^1.8.3

  # Utilities (محسن)
  uuid: ^4.2.1
  intl: ^0.19.0
  shared_preferences: ^2.5.3
  device_info_plus: ^11.5.0
  package_info_plus: ^8.0.2

  # Local Storage (محسن)
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  firebase_messaging: ^15.1.3
  cloud_functions: ^5.1.3
  flutter_local_notifications: ^17.2.3

  # HTTP requests
  http: ^1.1.0
  google_sign_in: ^6.2.1

  # Email verification (crypto مطلوب لـ Firebase)
  crypto: ^3.0.3
  firebase_database: ^11.3.9
  url_launcher: ^6.2.5

  permission_handler: ^12.0.1
  flutter_secure_storage: ^9.2.2

  # مكتبات التحميل والملفات
  dio: ^5.4.0

  # مكتبات عرض PDF محسنة
  flutter_cached_pdfview: ^0.4.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

  # Code generation
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # تحسينات الأداء والحجم
  generate: true

  # تحسين الخطوط
  fonts:
    - family: Cairo
      fonts:
        - asset: packages/google_fonts/fonts/Cairo-Regular.ttf
          weight: 400
        - asset: packages/google_fonts/fonts/Cairo-Bold.ttf
          weight: 700

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/pdfs/sample.pdf  # ملف PDF التجريبي فقط
    - assets/icon/app_icon.png  # أيقونة التطبيق فقط

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# إعدادات أيقونة التطبيق - ساحة الشريعة والقانون
flutter_launcher_icons:
  android: true
  ios: false
  image_path: "assets/icon/app_icon.png"
  min_sdk_android: 21
  adaptive_icon_background: "#1E3A8A"  # أزرق قانوني
  adaptive_icon_foreground: "assets/icon/app_icon.png"
